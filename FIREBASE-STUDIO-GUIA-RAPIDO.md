# 🚀 Guia R<PERSON>pido - Firebase Studio VPS

## 📋 O que você precisa fazer:

### 1. Na VPS do Firebase Studio (dentro do cluster):

Abra o terminal e execute estes comandos:

```bash
# Atualizar sistema
sudo apt update && sudo apt upgrade -y

# Instalar Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Instalar PM2
sudo npm install -g pm2

# Criar diretório do projeto
mkdir -p ~/discord-bot-hosting
cd ~/discord-bot-hosting

# Criar arquivo de configuração
cat > .env << 'EOF'
PORT=3000
NODE_ENV=production
KEEP_ALIVE_INTERVAL=120000
PING_ENDPOINTS=https://google.com,https://discord.com,https://github.com
MAX_FILE_SIZE=25MB
LOG_RETENTION_DAYS=2
JWT_SECRET=sua-chave-secreta-aqui
ADMIN_PASSWORD=admin123
EOF

# Criar script de inicialização
cat > start.sh << 'EOF'
#!/bin/bash
echo "🚀 Iniciando Discord Bot Hosting..."
if [ ! -d "node_modules" ]; then
    npm install
fi
pm2 start server.js --name discord-bot-hosting
pm2 status
echo "✅ Sistema iniciado em http://localhost:3000"
EOF

chmod +x start.sh

# Criar script keep-alive
cat > keep-alive.sh << 'EOF'
#!/bin/bash
while true; do
    echo "$(date): Mantendo VPS ativa" >> ~/activity.log
    curl -s https://google.com > /dev/null
    curl -s https://discord.com > /dev/null
    dd if=/dev/zero of=/tmp/keepalive bs=1M count=5 2>/dev/null
    rm -f /tmp/keepalive
    if ! pm2 list | grep -q "discord-bot-hosting.*online"; then
        cd ~/discord-bot-hosting && ./start.sh
    fi
    sleep 90
done
EOF

chmod +x keep-alive.sh

# Iniciar servidor HTTP temporário para receber arquivos
python3 -m http.server 8080 --bind 0.0.0.0 &
echo "Servidor HTTP iniciado na porta 8080"
```

### 2. No seu computador local:

```bash
# Navegar para o diretório do projeto
cd c:\Users\<USER>\Desktop\hospedagem

# Criar arquivo compactado
tar -czf discord-bot-hosting.tar.gz --exclude=node_modules --exclude=.git --exclude=bots --exclude=logs --exclude=uploads .

# Enviar para a VPS (substitua pela URL do seu cluster)
curl -X POST -F "file=@discord-bot-hosting.tar.gz" https://80-firebase-cluster-jupter-1752104404921.cluster-pgviq6mvsncnqxx6kr7pbz65v6.cloudworkstations.dev:8080/upload
```

### 3. De volta na VPS:

```bash
# Parar servidor HTTP temporário
pkill -f "python3 -m http.server"

# Extrair arquivos (se o upload funcionou)
tar -xzf discord-bot-hosting.tar.gz

# Instalar dependências
npm install

# Iniciar sistema
./start.sh

# Iniciar keep-alive em background
nohup ./keep-alive.sh > keep-alive.log 2>&1 &

# Verificar se está funcionando
pm2 status
curl http://localhost:3000
```

## 🌐 Acessar o Sistema:

### Opção 1: Dentro da VPS
- Abra um navegador na VPS
- Acesse: `http://localhost:3000`

### Opção 2: Port Forwarding (se disponível)
```bash
# Na VPS, verificar se a porta está aberta
sudo ufw allow 3000/tcp
netstat -tlnp | grep :3000
```

### Opção 3: Nginx Proxy
```bash
# Instalar nginx
sudo apt install nginx -y

# Configurar proxy
sudo tee /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# Reiniciar nginx
sudo systemctl restart nginx

# Agora acesse pela porta 80 (HTTP padrão)
```

## 🔧 Comandos Úteis:

```bash
# Ver status dos processos
pm2 status

# Ver logs em tempo real
pm2 logs discord-bot-hosting

# Reiniciar sistema
pm2 restart discord-bot-hosting

# Parar sistema
pm2 stop discord-bot-hosting

# Ver atividade do keep-alive
tail -f ~/activity.log

# Verificar se keep-alive está rodando
ps aux | grep keep-alive

# Limpar logs
pm2 flush

# Ver uso de recursos
htop
```

## 🚨 Se algo der errado:

```bash
# Verificar logs de erro
pm2 logs discord-bot-hosting --err

# Reiniciar tudo
pm2 delete discord-bot-hosting
./start.sh

# Verificar conectividade
ping google.com

# Verificar porta
netstat -tlnp | grep :3000

# Verificar arquivos
ls -la ~/discord-bot-hosting/
```

## 📱 Testando o Sistema:

1. Acesse `http://localhost:3000` (ou pela porta 80 se configurou nginx)
2. Você deve ver a interface do Discord Bot Hosting
3. Teste fazer upload de um bot ZIP
4. Verifique se o bot aparece na lista
5. Teste iniciar/parar bots

## 🔄 Manter VPS Ativa:

O script `keep-alive.sh` faz:
- Ping a cada 90 segundos
- Atividade de disco
- Verifica se o sistema principal está rodando
- Reinicia automaticamente se necessário

Para verificar se está funcionando:
```bash
tail -f ~/activity.log
```

## 📞 Suporte:

Se tiver problemas:
1. Verifique os logs: `pm2 logs`
2. Verifique o keep-alive: `tail ~/activity.log`
3. Reinicie: `pm2 restart discord-bot-hosting`
4. Verifique conectividade: `ping google.com`

---

**🎯 Resumo dos comandos principais:**

Na VPS:
```bash
mkdir -p ~/discord-bot-hosting && cd ~/discord-bot-hosting
# (executar todos os comandos da seção 1)
```

No seu PC:
```bash
cd c:\Users\<USER>\Desktop\hospedagem
tar -czf discord-bot-hosting.tar.gz --exclude=node_modules .
# (enviar arquivo para VPS)
```

Na VPS novamente:
```bash
tar -xzf discord-bot-hosting.tar.gz
npm install
./start.sh
nohup ./keep-alive.sh &
```

Pronto! Sistema funcionando em `http://localhost:3000` 🚀
