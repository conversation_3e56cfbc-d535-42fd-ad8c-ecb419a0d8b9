#!/bin/bash

# Script para fazer deploy no Firebase Studio VPS
# Execute este script dentro da VPS do Firebase Studio

echo "🚀 Configurando Discord Bot Hosting no Firebase Studio VPS..."

# 1. Atualizar sistema
echo "📦 Atualizando sistema..."
sudo apt update && sudo apt upgrade -y

# 2. Instalar Node.js 18
echo "📦 Instalando Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 3. Instalar PM2 globalmente
echo "📦 Instalando PM2..."
sudo npm install -g pm2

# 4. Instalar dependências do sistema
echo "📦 Instalando dependências do sistema..."
sudo apt install -y git curl wget unzip build-essential

# 5. Criar diretório do projeto
echo "📁 Criando estrutura de diretórios..."
mkdir -p ~/discord-bot-hosting
cd ~/discord-bot-hosting

# 6. Baixar arquivos do projeto (você pode usar git clone ou upload manual)
echo "📥 Preparando para receber arquivos do projeto..."
mkdir -p {routes,services,utils,config,client/public,uploads,bots,logs,temp,keys}

# 7. Criar arquivo de configuração para VPS
cat > .env << EOF
# Configurações da VPS
PORT=3000
NODE_ENV=production

# Configurações da VPS Firebase Studio
VPS_HOST=localhost
VPS_PORT=22
VPS_USERNAME=$(whoami)

# Configurações de Segurança
JWT_SECRET=$(openssl rand -base64 32)
ADMIN_PASSWORD=admin123

# Configurações do Keep-Alive
KEEP_ALIVE_INTERVAL=180000
PING_ENDPOINTS=https://google.com,https://discord.com,https://github.com

# Configurações de Logs
LOG_LEVEL=info
LOG_RETENTION_DAYS=3

# Configurações de Upload
MAX_FILE_SIZE=50MB
ALLOWED_EXTENSIONS=.js,.json,.txt,.md,.env
EOF

# 8. Criar script de inicialização
cat > start.sh << 'EOF'
#!/bin/bash
echo "🚀 Iniciando Discord Bot Hosting System..."

# Verificar se o Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não encontrado!"
    exit 1
fi

# Verificar se o PM2 está instalado
if ! command -v pm2 &> /dev/null; then
    echo "📦 Instalando PM2..."
    npm install -g pm2
fi

# Instalar dependências se necessário
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    npm install
fi

# Iniciar com PM2
echo "▶️ Iniciando servidor com PM2..."
pm2 start ecosystem.config.json

# Mostrar status
pm2 status

echo "✅ Sistema iniciado!"
echo "🌐 Acesse: http://localhost:3000"
echo "📊 Logs: pm2 logs discord-bot-hosting"
EOF

chmod +x start.sh

# 9. Criar script de parada
cat > stop.sh << 'EOF'
#!/bin/bash
echo "⏹️ Parando Discord Bot Hosting System..."
pm2 stop discord-bot-hosting
pm2 delete discord-bot-hosting
echo "✅ Sistema parado!"
EOF

chmod +x stop.sh

# 10. Criar configuração do PM2
cat > ecosystem.config.json << 'EOF'
{
  "apps": [{
    "name": "discord-bot-hosting",
    "script": "server.js",
    "instances": 1,
    "autorestart": true,
    "watch": false,
    "max_memory_restart": "512M",
    "env": {
      "NODE_ENV": "production",
      "PORT": 3000
    },
    "error_file": "./logs/pm2-error.log",
    "out_file": "./logs/pm2-out.log",
    "log_file": "./logs/pm2-combined.log",
    "time": true,
    "max_restarts": 10,
    "min_uptime": "10s"
  }]
}
EOF

# 11. Criar script de keep-alive específico para Firebase Studio
cat > keep-alive-vps.sh << 'EOF'
#!/bin/bash

# Script para manter a VPS ativa no Firebase Studio
# Execute em background: nohup ./keep-alive-vps.sh &

while true; do
    # Atividade de CPU
    echo "🔄 Mantendo VPS ativa..." >> ~/vps-activity.log
    date >> ~/vps-activity.log
    
    # Simular atividade
    dd if=/dev/zero of=/tmp/keepalive bs=1M count=10 2>/dev/null
    rm -f /tmp/keepalive
    
    # Ping para manter conexão
    ping -c 1 google.com > /dev/null 2>&1
    
    # Verificar se o sistema principal está rodando
    if ! pm2 list | grep -q "discord-bot-hosting.*online"; then
        echo "⚠️ Sistema principal offline, reiniciando..." >> ~/vps-activity.log
        ./start.sh
    fi
    
    # Aguardar 3 minutos
    sleep 180
done
EOF

chmod +x keep-alive-vps.sh

# 12. Criar script de upload de arquivos
cat > upload-project.sh << 'EOF'
#!/bin/bash

echo "📤 Script para upload dos arquivos do projeto"
echo "Execute este comando no seu computador local:"
echo ""
echo "# Compactar projeto"
echo "tar -czf discord-bot-hosting.tar.gz --exclude=node_modules --exclude=.git --exclude=bots --exclude=logs --exclude=uploads ."
echo ""
echo "# Upload via SCP (substitua pelo seu endpoint)"
echo "scp discord-bot-hosting.tar.gz user@your-vps:/home/<USER>/discord-bot-hosting/"
echo ""
echo "# Ou use este comando curl se tiver acesso HTTP:"
echo "curl -X POST -F 'file=@discord-bot-hosting.tar.gz' http://your-vps:3000/api/upload-system"
echo ""
echo "Depois execute na VPS:"
echo "cd ~/discord-bot-hosting"
echo "tar -xzf discord-bot-hosting.tar.gz"
echo "npm install"
echo "./start.sh"
EOF

chmod +x upload-project.sh

# 13. Configurar firewall (se necessário)
echo "🔥 Configurando firewall..."
sudo ufw allow 3000/tcp 2>/dev/null || echo "UFW não disponível"

# 14. Criar serviço systemd para auto-start
sudo tee /etc/systemd/system/discord-bot-hosting.service > /dev/null << EOF
[Unit]
Description=Discord Bot Hosting System
After=network.target

[Service]
Type=forking
User=$(whoami)
WorkingDirectory=/home/<USER>/discord-bot-hosting
ExecStart=/home/<USER>/discord-bot-hosting/start.sh
ExecStop=/home/<USER>/discord-bot-hosting/stop.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Habilitar serviço
sudo systemctl daemon-reload
sudo systemctl enable discord-bot-hosting

echo ""
echo "✅ Configuração da VPS concluída!"
echo ""
echo "📋 Próximos passos:"
echo "1. Faça upload dos arquivos do projeto para esta VPS"
echo "2. Execute: npm install"
echo "3. Execute: ./start.sh"
echo "4. Execute em background: nohup ./keep-alive-vps.sh &"
echo ""
echo "🌐 O sistema estará disponível em:"
echo "   http://localhost:3000 (local na VPS)"
echo "   Ou configure port forwarding para acesso externo"
echo ""
echo "📊 Comandos úteis:"
echo "   ./start.sh     - Iniciar sistema"
echo "   ./stop.sh      - Parar sistema"
echo "   pm2 logs       - Ver logs"
echo "   pm2 status     - Ver status"
echo ""
