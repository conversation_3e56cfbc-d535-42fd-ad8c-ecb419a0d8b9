#!/bin/bash

# Script de configuração automática para Firebase Studio VPS
# Execute este script dentro da sua VPS do Firebase Studio

echo "🔥 Configuração Automática - Discord Bot Hosting no Firebase Studio"
echo "=================================================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se está rodando como usuário normal (não root)
if [ "$EUID" -eq 0 ]; then
    log_error "Não execute este script como root!"
    exit 1
fi

log_info "Iniciando configuração automática..."

# 1. Atualizar sistema
log_info "Atualizando sistema..."
sudo apt update && sudo apt upgrade -y
if [ $? -eq 0 ]; then
    log_success "Sistema atualizado"
else
    log_error "Falha ao atualizar sistema"
    exit 1
fi

# 2. Instalar Node.js 18
log_info "Instalando Node.js 18..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    log_success "Node.js instalado: $NODE_VERSION"
else
    log_error "Falha ao instalar Node.js"
    exit 1
fi

# 3. Instalar PM2
log_info "Instalando PM2..."
sudo npm install -g pm2
if command -v pm2 &> /dev/null; then
    log_success "PM2 instalado"
else
    log_error "Falha ao instalar PM2"
    exit 1
fi

# 4. Instalar dependências do sistema
log_info "Instalando dependências do sistema..."
sudo apt install -y git curl wget unzip build-essential python3 nginx htop
log_success "Dependências instaladas"

# 5. Criar estrutura do projeto
log_info "Criando estrutura do projeto..."
mkdir -p ~/discord-bot-hosting
cd ~/discord-bot-hosting

# Criar diretórios necessários
mkdir -p {routes,services,utils,config,client/public,uploads,bots,logs,temp,keys}
log_success "Estrutura de diretórios criada"

# 6. Criar arquivo de configuração
log_info "Criando arquivo de configuração..."
cat > .env << 'EOF'
# Configurações da VPS Firebase Studio
PORT=3000
NODE_ENV=production

# Keep-Alive otimizado para Firebase Studio
KEEP_ALIVE_INTERVAL=90000
PING_ENDPOINTS=https://google.com,https://discord.com,https://github.com,https://firebase.google.com

# Configurações de recursos (Firebase Studio tem limitações)
MAX_FILE_SIZE=25MB
MAX_CONCURRENT_BOTS=3
LOG_RETENTION_DAYS=2

# Configurações de memória
NODE_OPTIONS=--max-old-space-size=512

# Segurança
JWT_SECRET=firebase-studio-discord-bot-hosting-2024
ADMIN_PASSWORD=admin123

# Logs
LOG_LEVEL=info
EOF

log_success "Arquivo .env criado"

# 7. Criar script de inicialização
log_info "Criando script de inicialização..."
cat > start.sh << 'EOF'
#!/bin/bash
echo "🚀 Iniciando Discord Bot Hosting System..."

# Verificar Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não encontrado!"
    exit 1
fi

# Instalar dependências se necessário
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    npm install --production
fi

# Parar processo anterior se existir
pm2 delete discord-bot-hosting 2>/dev/null || true

# Iniciar com PM2
echo "▶️ Iniciando servidor..."
pm2 start server.js --name discord-bot-hosting --max-memory-restart 400M

# Configurar auto-restart
pm2 startup
pm2 save

# Mostrar status
pm2 status

echo "✅ Sistema iniciado!"
echo "🌐 Acesse: http://localhost:3000"
echo "📊 Logs: pm2 logs discord-bot-hosting"
EOF

chmod +x start.sh
log_success "Script de inicialização criado"

# 8. Criar script de parada
log_info "Criando script de parada..."
cat > stop.sh << 'EOF'
#!/bin/bash
echo "⏹️ Parando Discord Bot Hosting System..."
pm2 stop discord-bot-hosting
pm2 delete discord-bot-hosting
echo "✅ Sistema parado!"
EOF

chmod +x stop.sh
log_success "Script de parada criado"

# 9. Criar script keep-alive otimizado para Firebase Studio
log_info "Criando script keep-alive..."
cat > keep-alive-firebase.sh << 'EOF'
#!/bin/bash

# Keep-Alive otimizado para Firebase Studio VPS
LOG_FILE="$HOME/firebase-keepalive.log"

log_activity() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" >> "$LOG_FILE"
}

log_activity "Keep-Alive iniciado"

while true; do
    # Atividade de rede - múltiplos endpoints
    curl -s --max-time 10 https://google.com > /dev/null 2>&1
    curl -s --max-time 10 https://discord.com > /dev/null 2>&1
    curl -s --max-time 10 https://github.com > /dev/null 2>&1
    curl -s --max-time 10 https://firebase.google.com > /dev/null 2>&1
    
    # Atividade de CPU leve
    echo "scale=1000; 4*a(1)" | bc -l > /dev/null 2>&1
    
    # Atividade de disco
    dd if=/dev/zero of=/tmp/keepalive bs=1M count=3 2>/dev/null
    rm -f /tmp/keepalive
    
    # Simular atividade de usuário (se X11 disponível)
    if command -v xdotool &> /dev/null; then
        xdotool mousemove 100 100 2>/dev/null || true
        xdotool mousemove 200 200 2>/dev/null || true
    fi
    
    # Verificar sistema principal
    if ! pm2 list | grep -q "discord-bot-hosting.*online"; then
        log_activity "Sistema principal offline, reiniciando..."
        cd ~/discord-bot-hosting && ./start.sh
        log_activity "Sistema reiniciado"
    fi
    
    # Log de atividade a cada 10 ciclos (15 minutos)
    if [ $(($(date +%s) % 900)) -lt 90 ]; then
        log_activity "Keep-alive ativo - Sistema funcionando"
        
        # Limpar log se muito grande (> 1MB)
        if [ -f "$LOG_FILE" ] && [ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null || echo 0) -gt 1048576 ]; then
            tail -n 100 "$LOG_FILE" > "$LOG_FILE.tmp" && mv "$LOG_FILE.tmp" "$LOG_FILE"
            log_activity "Log limpo"
        fi
    fi
    
    # Aguardar 90 segundos
    sleep 90
done
EOF

chmod +x keep-alive-firebase.sh
log_success "Script keep-alive criado"

# 10. Configurar Nginx como proxy reverso
log_info "Configurando Nginx..."
sudo tee /etc/nginx/sites-available/discord-bot-hosting << 'EOF'
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    
    server_name _;
    
    # Aumentar limites para upload
    client_max_body_size 50M;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # Servir arquivos estáticos diretamente
    location /static/ {
        alias /home/<USER>/discord-bot-hosting/client/public/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Ativar site
sudo rm -f /etc/nginx/sites-enabled/default
sudo ln -s /etc/nginx/sites-available/discord-bot-hosting /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl restart nginx

log_success "Nginx configurado"

# 11. Configurar firewall
log_info "Configurando firewall..."
sudo ufw --force enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 3000/tcp
log_success "Firewall configurado"

# 12. Criar script de upload
log_info "Criando script de upload..."
cat > upload-receiver.py << 'EOF'
#!/usr/bin/env python3
import http.server
import socketserver
import cgi
import os
import tempfile
from urllib.parse import parse_qs

class UploadHandler(http.server.SimpleHTTPRequestHandler):
    def do_POST(self):
        if self.path == '/upload':
            form = cgi.FieldStorage(
                fp=self.rfile,
                headers=self.headers,
                environ={'REQUEST_METHOD': 'POST'}
            )
            
            if 'file' in form:
                fileitem = form['file']
                if fileitem.filename:
                    # Salvar arquivo
                    with open(fileitem.filename, 'wb') as f:
                        f.write(fileitem.file.read())
                    
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    self.wfile.write(b'{"success": true, "message": "Upload concluido"}')
                    return
            
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(b'{"error": "Nenhum arquivo enviado"}')
        else:
            super().do_POST()

PORT = 8080
with socketserver.TCPServer(("", PORT), UploadHandler) as httpd:
    print(f"Servidor de upload rodando na porta {PORT}")
    httpd.serve_forever()
EOF

chmod +x upload-receiver.py
log_success "Script de upload criado"

# 13. Criar script de monitoramento
log_info "Criando script de monitoramento..."
cat > monitor.sh << 'EOF'
#!/bin/bash

echo "📊 Status do Discord Bot Hosting System"
echo "======================================"

echo "🖥️  Sistema:"
echo "   Uptime: $(uptime -p)"
echo "   Load: $(uptime | awk -F'load average:' '{print $2}')"
echo "   Memória: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2}')"
echo "   Disco: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 " usado)"}')"

echo ""
echo "🤖 PM2 Processos:"
pm2 status

echo ""
echo "🔄 Keep-Alive:"
if pgrep -f "keep-alive-firebase.sh" > /dev/null; then
    echo "   Status: ✅ Ativo"
    echo "   PID: $(pgrep -f keep-alive-firebase.sh)"
    echo "   Últimas atividades:"
    tail -n 5 ~/firebase-keepalive.log 2>/dev/null || echo "   Nenhum log disponível"
else
    echo "   Status: ❌ Inativo"
fi

echo ""
echo "🌐 Conectividade:"
if curl -s --max-time 5 http://localhost:3000 > /dev/null; then
    echo "   Sistema local: ✅ Online"
else
    echo "   Sistema local: ❌ Offline"
fi

if curl -s --max-time 5 https://google.com > /dev/null; then
    echo "   Internet: ✅ Online"
else
    echo "   Internet: ❌ Offline"
fi
EOF

chmod +x monitor.sh
log_success "Script de monitoramento criado"

# 14. Criar instruções finais
log_info "Criando instruções finais..."
cat > INSTRUCOES.md << 'EOF'
# 🎉 Configuração Concluída!

## 📋 Próximos Passos:

### 1. Enviar arquivos do projeto:

**No seu computador local:**
```bash
cd c:\Users\<USER>\Desktop\hospedagem
tar -czf discord-bot-hosting.tar.gz --exclude=node_modules --exclude=.git --exclude=bots --exclude=logs --exclude=uploads .
```

**Na VPS, inicie o servidor de upload:**
```bash
cd ~/discord-bot-hosting
python3 upload-receiver.py
```

**No seu computador, envie o arquivo:**
```bash
curl -X POST -F "file=@discord-bot-hosting.tar.gz" http://SEU-CLUSTER-URL:8080/upload
```

### 2. Fazer deploy:

```bash
# Parar servidor de upload
pkill -f upload-receiver.py

# Extrair arquivos
tar -xzf discord-bot-hosting.tar.gz

# Instalar e iniciar
npm install
./start.sh

# Iniciar keep-alive
nohup ./keep-alive-firebase.sh > /dev/null 2>&1 &
```

### 3. Acessar sistema:

- **Local:** http://localhost:3000
- **Nginx:** http://SEU-CLUSTER-URL (porta 80)

## 🔧 Comandos Úteis:

```bash
./monitor.sh          # Ver status completo
pm2 logs              # Ver logs
pm2 restart all       # Reiniciar
./stop.sh             # Parar sistema
```

## 🚨 Troubleshooting:

```bash
# Se algo não funcionar:
./stop.sh
./start.sh
nohup ./keep-alive-firebase.sh > /dev/null 2>&1 &

# Ver logs de erro:
pm2 logs --err

# Verificar keep-alive:
tail -f ~/firebase-keepalive.log
```
EOF

log_success "Instruções criadas"

# 15. Resumo final
echo ""
echo "🎉 CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!"
echo "====================================="
echo ""
log_success "Todos os scripts foram criados em: ~/discord-bot-hosting"
echo ""
echo "📋 Arquivos criados:"
echo "   ✅ .env (configuração)"
echo "   ✅ start.sh (iniciar sistema)"
echo "   ✅ stop.sh (parar sistema)"
echo "   ✅ keep-alive-firebase.sh (manter VPS ativa)"
echo "   ✅ upload-receiver.py (receber arquivos)"
echo "   ✅ monitor.sh (monitoramento)"
echo "   ✅ INSTRUCOES.md (próximos passos)"
echo ""
echo "🚀 Próximos passos:"
echo "   1. Leia o arquivo INSTRUCOES.md"
echo "   2. Envie os arquivos do seu projeto"
echo "   3. Execute ./start.sh"
echo "   4. Inicie o keep-alive"
echo ""
echo "📞 Para monitorar: ./monitor.sh"
echo "📖 Para instruções: cat INSTRUCOES.md"
echo ""
log_success "Sistema pronto para receber seu projeto!"

# Mostrar informações do sistema
echo ""
echo "ℹ️  Informações do sistema:"
echo "   Node.js: $(node --version)"
echo "   NPM: $(npm --version)"
echo "   PM2: $(pm2 --version)"
echo "   Usuário: $(whoami)"
echo "   Diretório: $(pwd)"
echo ""
