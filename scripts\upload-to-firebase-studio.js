#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');
const axios = require('axios');
const FormData = require('form-data');

class FirebaseStudioUploader {
    constructor() {
        this.projectRoot = path.join(__dirname, '..');
        this.excludePatterns = [
            'node_modules',
            '.git',
            'bots',
            'logs',
            'uploads',
            'temp',
            '.env',
            '*.log',
            'backup-*'
        ];
    }

    async createProjectArchive() {
        console.log('📦 Criando arquivo do projeto...');
        
        const outputPath = path.join(this.projectRoot, 'discord-bot-hosting.tar.gz');
        const output = fs.createWriteStream(outputPath);
        const archive = archiver('tar', { gzip: true });

        return new Promise((resolve, reject) => {
            output.on('close', () => {
                console.log(`✅ Arquivo criado: ${archive.pointer()} bytes`);
                resolve(outputPath);
            });

            archive.on('error', reject);
            archive.pipe(output);

            // Adicionar arquivos do projeto
            this.addFilesToArchive(archive);
            archive.finalize();
        });
    }

    addFilesToArchive(archive) {
        const files = this.getProjectFiles();
        
        files.forEach(file => {
            const relativePath = path.relative(this.projectRoot, file);
            console.log(`   📄 Adicionando: ${relativePath}`);
            archive.file(file, { name: relativePath });
        });
    }

    getProjectFiles() {
        const files = [];
        
        const scanDirectory = (dir) => {
            const items = fs.readdirSync(dir);
            
            items.forEach(item => {
                const fullPath = path.join(dir, item);
                const relativePath = path.relative(this.projectRoot, fullPath);
                
                // Verificar se deve ser excluído
                if (this.shouldExclude(relativePath)) {
                    return;
                }
                
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    scanDirectory(fullPath);
                } else {
                    files.push(fullPath);
                }
            });
        };
        
        scanDirectory(this.projectRoot);
        return files;
    }

    shouldExclude(relativePath) {
        return this.excludePatterns.some(pattern => {
            if (pattern.includes('*')) {
                const regex = new RegExp(pattern.replace('*', '.*'));
                return regex.test(relativePath);
            }
            return relativePath.includes(pattern);
        });
    }

    async uploadToFirebaseStudio(archivePath, vpsUrl) {
        console.log('🚀 Enviando para Firebase Studio VPS...');
        
        try {
            const form = new FormData();
            form.append('file', fs.createReadStream(archivePath));
            form.append('type', 'project');
            
            const response = await axios.post(`${vpsUrl}/api/upload-project`, form, {
                headers: {
                    ...form.getHeaders(),
                },
                maxContentLength: Infinity,
                maxBodyLength: Infinity,
                timeout: 300000 // 5 minutos
            });
            
            console.log('✅ Upload concluído!');
            return response.data;
            
        } catch (error) {
            console.error('❌ Erro no upload:', error.message);
            throw error;
        }
    }

    async generateDeployScript() {
        const scriptContent = `#!/bin/bash

# Script de deploy automático para Firebase Studio
echo "🚀 Iniciando deploy do Discord Bot Hosting..."

# Verificar se o arquivo foi enviado
if [ ! -f "discord-bot-hosting.tar.gz" ]; then
    echo "❌ Arquivo do projeto não encontrado!"
    exit 1
fi

# Extrair arquivos
echo "📦 Extraindo arquivos..."
tar -xzf discord-bot-hosting.tar.gz

# Instalar dependências
echo "📦 Instalando dependências..."
npm install --production

# Configurar ambiente
echo "⚙️ Configurando ambiente..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "📝 Configure o arquivo .env antes de continuar!"
fi

# Dar permissões aos scripts
chmod +x scripts/*.sh

# Iniciar sistema
echo "▶️ Iniciando sistema..."
./start.sh

# Iniciar keep-alive
echo "🔄 Iniciando keep-alive..."
nohup ./keep-alive-vps.sh > keep-alive.log 2>&1 &

echo "✅ Deploy concluído!"
echo "🌐 Sistema disponível em: http://localhost:3000"
echo "📊 Logs: pm2 logs discord-bot-hosting"
`;

        const scriptPath = path.join(this.projectRoot, 'deploy-firebase.sh');
        await fs.writeFile(scriptPath, scriptContent);
        await fs.chmod(scriptPath, '755');
        
        console.log('✅ Script de deploy criado: deploy-firebase.sh');
        return scriptPath;
    }

    async createFirebaseStudioInstructions() {
        const instructions = `
# 🔥 Instruções para Firebase Studio VPS

## 1. Na sua VPS do Firebase Studio, execute:

\`\`\`bash
# Criar diretório do projeto
mkdir -p ~/discord-bot-hosting
cd ~/discord-bot-hosting

# Baixar e executar script de configuração
curl -o setup.sh https://raw.githubusercontent.com/seu-repo/scripts/deploy-to-vps.sh
chmod +x setup.sh
./setup.sh
\`\`\`

## 2. No seu computador local, execute:

\`\`\`bash
# Fazer upload do projeto
node scripts/upload-to-firebase-studio.js --url=http://SEU-CLUSTER-URL:3000
\`\`\`

## 3. De volta na VPS, execute:

\`\`\`bash
# Fazer deploy
./deploy-firebase.sh
\`\`\`

## 4. Configurar Keep-Alive:

\`\`\`bash
# Iniciar keep-alive em background
nohup ./keep-alive-vps.sh &

# Verificar se está rodando
ps aux | grep keep-alive
\`\`\`

## 5. Acessar o sistema:

- Local na VPS: http://localhost:3000
- Externo: Configure port forwarding ou nginx

## Comandos Úteis:

\`\`\`bash
# Ver status
pm2 status

# Ver logs
pm2 logs discord-bot-hosting

# Reiniciar
pm2 restart discord-bot-hosting

# Parar
pm2 stop discord-bot-hosting
\`\`\`
`;

        const instructionsPath = path.join(this.projectRoot, 'FIREBASE-STUDIO-SETUP.md');
        await fs.writeFile(instructionsPath, instructions);
        
        console.log('✅ Instruções criadas: FIREBASE-STUDIO-SETUP.md');
        return instructionsPath;
    }

    async run() {
        try {
            console.log('🚀 Firebase Studio Uploader\n');
            
            // Criar arquivo do projeto
            const archivePath = await this.createProjectArchive();
            
            // Gerar script de deploy
            await this.generateDeployScript();
            
            // Criar instruções
            await this.createFirebaseStudioInstructions();
            
            console.log('\n✅ Preparação concluída!');
            console.log('\n📋 Próximos passos:');
            console.log('1. Leia o arquivo FIREBASE-STUDIO-SETUP.md');
            console.log('2. Configure sua VPS do Firebase Studio');
            console.log('3. Execute o upload usando este script');
            console.log('\n📁 Arquivos criados:');
            console.log(`   - ${path.basename(archivePath)}`);
            console.log('   - deploy-firebase.sh');
            console.log('   - FIREBASE-STUDIO-SETUP.md');
            
        } catch (error) {
            console.error('\n❌ Erro:', error.message);
            process.exit(1);
        }
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    const uploader = new FirebaseStudioUploader();
    
    // Verificar argumentos da linha de comando
    const args = process.argv.slice(2);
    const urlArg = args.find(arg => arg.startsWith('--url='));
    
    if (urlArg) {
        const vpsUrl = urlArg.split('=')[1];
        console.log(`🎯 URL da VPS: ${vpsUrl}`);
        
        uploader.createProjectArchive()
            .then(archivePath => uploader.uploadToFirebaseStudio(archivePath, vpsUrl))
            .then(() => {
                console.log('\n✅ Upload concluído com sucesso!');
                console.log('Execute o script de deploy na sua VPS.');
            })
            .catch(error => {
                console.error('\n❌ Erro no upload:', error.message);
                process.exit(1);
            });
    } else {
        uploader.run();
    }
}

module.exports = FirebaseStudioUploader;
