const express = require('express');
const multer = require('multer');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs-extra');
const path = require('path');
const cors = require('cors');
const config = require('./config');

const app = express();

// Middleware
app.use(cors());
app.use(express.static('public'));
app.use(express.json());

// Configurar upload
const upload = multer({
    dest: 'temp-uploads/',
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB
    },
    fileFilter: (req, file, cb) => {
        const ext = path.extname(file.originalname).toLowerCase();
        if (config.ALLOWED_EXTENSIONS.includes(ext)) {
            cb(null, true);
        } else {
            cb(new Error('Tipo de arquivo não permitido. Use apenas ZIP ou RAR.'));
        }
    }
});

// Criar diretórios necessários
const initDirectories = async () => {
    await fs.ensureDir('temp-uploads');
    await fs.ensureDir('public');
    await fs.ensureDir('logs');
};

// Rate limiting simples
const rateLimitMap = new Map();

const rateLimit = (req, res, next) => {
    const ip = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    const windowStart = now - config.RATE_LIMIT.windowMs;
    
    if (!rateLimitMap.has(ip)) {
        rateLimitMap.set(ip, []);
    }
    
    const requests = rateLimitMap.get(ip).filter(time => time > windowStart);
    
    if (requests.length >= config.RATE_LIMIT.max) {
        return res.status(429).json({
            error: 'Muitas tentativas. Tente novamente em 15 minutos.'
        });
    }
    
    requests.push(now);
    rateLimitMap.set(ip, requests);
    next();
};

// Função para log de atividades
const logActivity = async (activity) => {
    const logEntry = {
        timestamp: new Date().toISOString(),
        ...activity
    };
    
    await fs.appendFile('logs/activity.log', JSON.stringify(logEntry) + '\n');
    console.log(`📝 ${logEntry.timestamp}: ${activity.action}`);
};

// Função para notificar via Discord (opcional)
const notifyDiscord = async (message) => {
    if (!config.DISCORD_WEBHOOK) return;
    
    try {
        await axios.post(config.DISCORD_WEBHOOK, {
            content: `🤖 **Bot Hosting System**\n${message}`
        });
    } catch (error) {
        console.error('Erro ao enviar notificação Discord:', error.message);
    }
};

// Página principal
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public/index.html'));
});

// Painel admin
app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public/admin.html'));
});

// Monitor em tempo real
app.get('/monitor', (req, res) => {
    res.sendFile(path.join(__dirname, 'public/monitor.html'));
});

// API: Upload de bot (usuários)
app.post('/api/upload-bot', rateLimit, upload.single('botFile'), async (req, res) => {
    try {
        const { botName, botToken, userEmail, botDescription } = req.body;
        
        if (!req.file) {
            return res.status(400).json({ error: 'Nenhum arquivo enviado' });
        }
        
        if (!botName || !botToken || !userEmail) {
            await fs.remove(req.file.path);
            return res.status(400).json({ error: 'Todos os campos são obrigatórios' });
        }
        
        console.log(`📤 Novo upload: ${botName} de ${userEmail}`);
        
        // Log da atividade
        await logActivity({
            action: 'upload_attempt',
            botName,
            userEmail,
            fileSize: req.file.size,
            ip: req.ip
        });
        
        // Preparar dados para envio à VPS
        const formData = new FormData();
        formData.append('botName', botName);
        formData.append('botToken', botToken);
        formData.append('botFile', fs.createReadStream(req.file.path), {
            filename: req.file.originalname
        });
        
        // Enviar para VPS Firebase Studio
        let vpsUrl = config.VPS_URL;
        let response;

        // Tentar primeiro na porta 80 (nginx), depois na 3000
        const urlsToTry = [
            config.VPS_URL,
            `${config.VPS_URL}:3000`
        ];

        for (const url of urlsToTry) {
            try {
                console.log(`Tentando enviar para: ${url}`);
                response = await axios.post(`${url}/upload`, formData, {
                    headers: {
                        ...formData.getHeaders(),
                        'User-Agent': 'Discord-Bot-Hosting-Site/1.0'
                    },
                    timeout: 30000, // 30 segundos
                    maxContentLength: Infinity,
                    maxBodyLength: Infinity
                });
                console.log(`✅ Sucesso em: ${url}`);
                break;
            } catch (error) {
                console.log(`❌ Falha em ${url}: ${error.message}`);
                if (url === urlsToTry[urlsToTry.length - 1]) {
                    throw error; // Se foi a última tentativa, propagar o erro
                }
            }
        }
        
        // Limpar arquivo temporário
        await fs.remove(req.file.path);
        
        if (response.data.success) {
            // Salvar registro do usuário
            const userRecord = {
                botId: response.data.botId,
                botName,
                userEmail,
                botDescription: botDescription || '',
                uploadDate: new Date().toISOString(),
                status: 'uploaded',
                fileSize: req.file.size,
                originalName: req.file.originalname
            };
            
            await fs.appendFile('logs/user-bots.log', JSON.stringify(userRecord) + '\n');
            
            // Log de sucesso
            await logActivity({
                action: 'upload_success',
                botId: response.data.botId,
                botName,
                userEmail
            });
            
            // Notificar Discord
            await notifyDiscord(`✅ Novo bot hospedado!\n**Nome:** ${botName}\n**Email:** ${userEmail}\n**ID:** ${response.data.botId}`);
            
            res.json({
                success: true,
                message: 'Bot enviado com sucesso! Será processado em alguns minutos.',
                botId: response.data.botId,
                estimatedTime: '2-5 minutos'
            });
        } else {
            await logActivity({
                action: 'upload_failed',
                botName,
                userEmail,
                error: response.data.error
            });
            
            res.status(400).json({ error: response.data.error });
        }
        
    } catch (error) {
        console.error('Erro no upload:', error);
        
        // Limpar arquivo em caso de erro
        if (req.file) {
            await fs.remove(req.file.path).catch(() => {});
        }
        
        await logActivity({
            action: 'upload_error',
            error: error.message,
            userEmail: req.body.userEmail
        });
        
        if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
            res.status(503).json({ 
                error: 'Servidor de hospedagem temporariamente indisponível. Tente novamente em alguns minutos.' 
            });
        } else {
            res.status(500).json({ error: 'Erro interno do servidor' });
        }
    }
});

// API: Status da VPS
app.get('/api/vps-status', async (req, res) => {
    try {
        const response = await axios.get(`${config.VPS_URL}/bots`, { 
            timeout: 10000,
            headers: {
                'User-Agent': 'Discord-Bot-Hosting-Site/1.0'
            }
        });
        
        res.json({
            online: true,
            bots: response.data.length,
            lastCheck: new Date().toISOString(),
            vpsUrl: config.VPS_URL
        });
    } catch (error) {
        console.error('Erro ao verificar status da VPS:', error.message);
        
        res.json({
            online: false,
            error: error.message,
            lastCheck: new Date().toISOString(),
            vpsUrl: config.VPS_URL
        });
    }
});

// API: Estatísticas públicas
app.get('/api/stats', async (req, res) => {
    try {
        // Contar bots hospedados
        const userBotsLog = 'logs/user-bots.log';
        let totalBots = 0;
        
        if (await fs.pathExists(userBotsLog)) {
            const content = await fs.readFile(userBotsLog, 'utf8');
            totalBots = content.split('\n').filter(line => line.trim()).length;
        }
        
        // Status da VPS
        let vpsOnline = false;
        let activeBots = 0;
        
        try {
            const vpsResponse = await axios.get(`${config.VPS_URL}/bots`, { timeout: 5000 });
            vpsOnline = true;
            activeBots = vpsResponse.data.length;
        } catch (error) {
            // VPS offline
        }
        
        res.json({
            totalBotsHosted: totalBots,
            activeBotsNow: activeBots,
            systemOnline: vpsOnline,
            lastUpdate: new Date().toISOString()
        });
        
    } catch (error) {
        res.status(500).json({ error: 'Erro ao obter estatísticas' });
    }
});

// API: Listar bots (admin)
app.get('/api/admin/bots', async (req, res) => {
    try {
        const { password } = req.query;
        if (password !== config.ADMIN_PASSWORD) {
            return res.status(401).json({ error: 'Senha incorreta' });
        }
        
        const response = await axios.get(`${config.VPS_URL}/bots`, {
            timeout: 10000
        });
        
        res.json(response.data);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// API: Controlar bot (admin)
app.post('/api/admin/bots/:id/:action', async (req, res) => {
    try {
        const { password } = req.body;
        if (password !== config.ADMIN_PASSWORD) {
            return res.status(401).json({ error: 'Senha incorreta' });
        }
        
        const { id, action } = req.params;
        const response = await axios.post(`${config.VPS_URL}/bots/${id}/${action}`, {}, {
            timeout: 30000
        });
        
        await logActivity({
            action: `admin_${action}`,
            botId: id,
            admin: true
        });
        
        res.json(response.data);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// API: Logs de atividade (admin)
app.get('/api/admin/logs', async (req, res) => {
    try {
        const { password, limit = 100 } = req.query;
        if (password !== config.ADMIN_PASSWORD) {
            return res.status(401).json({ error: 'Senha incorreta' });
        }
        
        const logFile = 'logs/activity.log';
        if (!await fs.pathExists(logFile)) {
            return res.json([]);
        }
        
        const content = await fs.readFile(logFile, 'utf8');
        const logs = content.split('\n')
            .filter(line => line.trim())
            .map(line => {
                try {
                    return JSON.parse(line);
                } catch {
                    return null;
                }
            })
            .filter(log => log !== null)
            .slice(-parseInt(limit))
            .reverse();
        
        res.json(logs);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Inicializar servidor
const startServer = async () => {
    try {
        await initDirectories();
        
        app.listen(config.SITE_PORT, () => {
            console.log(`🌐 Site principal rodando na porta ${config.SITE_PORT}`);
            console.log(`🔗 VPS configurada: ${config.VPS_URL}`);
            console.log(`📱 Acesse: http://localhost:${config.SITE_PORT}`);
            console.log(`🔧 Admin: http://localhost:${config.SITE_PORT}/admin`);
        });
        
        // Log de inicialização
        await logActivity({
            action: 'server_started',
            port: config.SITE_PORT,
            vpsUrl: config.VPS_URL
        });
        
    } catch (error) {
        console.error('❌ Erro ao inicializar servidor:', error);
        process.exit(1);
    }
};

startServer();
