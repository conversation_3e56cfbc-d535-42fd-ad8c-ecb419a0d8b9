# 🔥 Configuração Firebase Studio VPS

Guia completo para configurar o sistema de hospedagem de bots Discord na VPS do Firebase Studio.

## 📋 Pré-requisitos

- Acesso ao Firebase Studio VPS
- URL do cluster: `https://80-firebase-cluster-jupter-1752104404921.cluster-pgviq6mvsncnqxx6kr7pbz65v6.cloudworkstations.dev/vnc.html?autoconnect=true&resize=remote`

## 🚀 Passo a Passo

### 1. Acessar a VPS

1. Abra o link do seu cluster Firebase Studio
2. Aguarde carregar o ambiente VNC
3. Abra um terminal (geralmente Ctrl+Alt+T ou pelo menu)

### 2. Executar Script de Configuração

No terminal da VPS, execute:

```bash
# Baixar script de configuração
curl -o setup-vps.sh https://raw.githubusercontent.com/seu-repo/discord-bot-hosting/main/scripts/deploy-to-vps.sh

# Dar permissão de execução
chmod +x setup-vps.sh

# Executar configuração
./setup-vps.sh
```

### 3. Upload dos Arquivos do Projeto

**Opção A: Via Git (Recomendado)**
```bash
cd ~/discord-bot-hosting
git clone https://github.com/seu-usuario/discord-bot-hosting.git .
```

**Opção B: Upload Manual**
1. No seu computador local, compacte o projeto:
```bash
tar -czf discord-bot-hosting.tar.gz --exclude=node_modules --exclude=.git --exclude=bots --exclude=logs --exclude=uploads .
```

2. Na VPS, crie um servidor HTTP temporário para receber o arquivo:
```bash
cd ~/discord-bot-hosting
python3 -m http.server 8080 --bind 0.0.0.0
```

3. No seu computador, envie o arquivo:
```bash
curl -X POST -F "file=@discord-bot-hosting.tar.gz" http://SEU-CLUSTER-URL:8080/upload
```

4. Na VPS, extraia os arquivos:
```bash
tar -xzf discord-bot-hosting.tar.gz
```

### 4. Instalar Dependências e Iniciar

```bash
cd ~/discord-bot-hosting

# Instalar dependências
npm install

# Iniciar sistema
./start.sh

# Iniciar keep-alive em background
nohup ./keep-alive-vps.sh &
```

### 5. Configurar Acesso Externo

Para acessar o sistema de fora da VPS:

**Opção A: Port Forwarding (se disponível)**
```bash
# Verificar se a porta está aberta
netstat -tlnp | grep :3000

# Configurar iptables se necessário
sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
```

**Opção B: Túnel SSH**
No seu computador local:
```bash
ssh -L 3000:localhost:3000 user@your-vps-ip
```

**Opção C: Nginx Reverse Proxy**
```bash
# Instalar nginx
sudo apt install nginx

# Configurar proxy
sudo tee /etc/nginx/sites-available/discord-bot-hosting << EOF
server {
    listen 80;
    server_name _;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Ativar site
sudo ln -s /etc/nginx/sites-available/discord-bot-hosting /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🔧 Configurações Específicas Firebase Studio

### Variáveis de Ambiente (.env)

```env
# Configurações da VPS Firebase Studio
PORT=3000
NODE_ENV=production

# Keep-Alive mais agressivo para Firebase Studio
KEEP_ALIVE_INTERVAL=120000  # 2 minutos
PING_ENDPOINTS=https://google.com,https://discord.com,https://github.com,https://firebase.google.com

# Configurações de recursos (Firebase Studio tem limitações)
MAX_FILE_SIZE=25MB
MAX_CONCURRENT_BOTS=5
LOG_RETENTION_DAYS=2

# Configurações de memória
NODE_OPTIONS=--max-old-space-size=512
```

### Script Keep-Alive Personalizado

O Firebase Studio pode desativar VPS inativas. Use este script:

```bash
# keep-alive-firebase.sh
#!/bin/bash

while true; do
    # Atividade de CPU
    echo "$(date): Mantendo Firebase Studio VPS ativa" >> ~/activity.log
    
    # Simular atividade de usuário
    xdotool mousemove 100 100
    xdotool mousemove 200 200
    
    # Atividade de rede
    curl -s https://google.com > /dev/null
    curl -s https://firebase.google.com > /dev/null
    
    # Atividade de disco
    dd if=/dev/zero of=/tmp/keepalive bs=1M count=5 2>/dev/null
    rm -f /tmp/keepalive
    
    # Verificar sistema principal
    if ! pm2 list | grep -q "discord-bot-hosting.*online"; then
        echo "$(date): Reiniciando sistema principal" >> ~/activity.log
        cd ~/discord-bot-hosting && ./start.sh
    fi
    
    # Aguardar 90 segundos
    sleep 90
done
```

## 📊 Monitoramento

### Verificar Status
```bash
# Status do PM2
pm2 status

# Logs em tempo real
pm2 logs discord-bot-hosting

# Status do sistema
htop

# Uso de disco
df -h

# Processos ativos
ps aux | grep node
```

### Comandos Úteis

```bash
# Reiniciar sistema
./stop.sh && ./start.sh

# Ver logs do keep-alive
tail -f ~/activity.log

# Verificar conectividade
ping google.com

# Verificar porta
netstat -tlnp | grep :3000

# Limpar logs
pm2 flush

# Backup de configurações
tar -czf backup-$(date +%Y%m%d).tar.gz .env ecosystem.config.json
```

## 🚨 Troubleshooting

### VPS Desconecta Frequentemente
1. Aumente a frequência do keep-alive
2. Use múltiplos scripts de atividade
3. Configure auto-restart mais agressivo

### Sistema Não Inicia
1. Verifique logs: `pm2 logs`
2. Verifique dependências: `npm install`
3. Verifique permissões: `chmod +x *.sh`

### Sem Acesso Externo
1. Verifique firewall: `sudo ufw status`
2. Configure nginx como proxy
3. Use túnel SSH

### Pouca Memória
1. Reduza `MAX_CONCURRENT_BOTS`
2. Configure `NODE_OPTIONS=--max-old-space-size=256`
3. Limpe logs regularmente

## 🔒 Segurança

```bash
# Configurar firewall básico
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 3000/tcp
sudo ufw allow 80/tcp

# Configurar fail2ban (opcional)
sudo apt install fail2ban

# Backup automático
crontab -e
# Adicionar: 0 2 * * * cd ~/discord-bot-hosting && tar -czf ~/backup-$(date +\%Y\%m\%d).tar.gz .
```

## 📞 Suporte

Se encontrar problemas:

1. Verifique os logs: `pm2 logs`
2. Consulte o arquivo de atividade: `tail ~/activity.log`
3. Reinicie o sistema: `./stop.sh && ./start.sh`
4. Verifique conectividade: `ping google.com`

---

**⚠️ Importante**: O Firebase Studio pode ter limitações de recursos e tempo de uso. Configure o keep-alive adequadamente e monitore o uso.
