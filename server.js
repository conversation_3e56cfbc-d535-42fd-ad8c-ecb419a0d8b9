const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static('public'));

// Criar diretórios necessários
const createDirectories = async () => {
    const dirs = [
        'uploads',
        'bots',
        'logs',
        'temp',
        'public/uploads'
    ];
    
    for (const dir of dirs) {
        await fs.ensureDir(dir);
    }
};

// Rotas
app.use('/api/upload', require('./routes/upload'));
app.use('/api/bots', require('./routes/bots'));
app.use('/api/vps', require('./routes/vps'));
app.use('/api/monitoring', require('./routes/monitoring'));

// Servir arquivos estáticos do cliente
app.use(express.static(path.join(__dirname, 'client/public')));

// Rota catch-all para SPA
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client/public/index.html'));
});

// Inicializar servidor
const startServer = async () => {
    try {
        await createDirectories();
        
        app.listen(PORT, () => {
            console.log(`🚀 Servidor rodando na porta ${PORT}`);
            console.log(`📱 Interface: http://localhost:${PORT}`);
        });
        
        // Inicializar serviços
        require('./services/keepAlive').start();
        require('./services/botManager').initialize();
        
    } catch (error) {
        console.error('❌ Erro ao inicializar servidor:', error);
        process.exit(1);
    }
};

startServer();

module.exports = app;
