const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static('public'));

// Criar diretórios necessários
const createDirectories = async () => {
    const dirs = [
        'uploads',
        'bots',
        'logs',
        'temp',
        'public/uploads'
    ];
    
    for (const dir of dirs) {
        await fs.ensureDir(dir);
    }
};

// Rotas
app.use('/api/upload', require('./routes/upload'));
app.use('/api/bots', require('./routes/bots'));
app.use('/api/vps', require('./routes/vps'));
app.use('/api/monitoring', require('./routes/monitoring'));

// Rota especial para upload do projeto completo (Firebase Studio)
const multer = require('multer');
const uploadProject = multer({ dest: 'temp/' });

app.post('/api/upload-project', uploadProject.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'Nenhum arquivo enviado' });
        }

        console.log('📦 Recebendo upload do projeto...');
        console.log(`   Arquivo: ${req.file.originalname}`);
        console.log(`   Tamanho: ${req.file.size} bytes`);

        // Mover arquivo para local definitivo
        const finalPath = path.join(__dirname, 'discord-bot-hosting.tar.gz');
        await fs.move(req.file.path, finalPath);

        res.json({
            success: true,
            message: 'Projeto enviado com sucesso!',
            filename: req.file.originalname,
            size: req.file.size,
            instructions: [
                'Execute: tar -xzf discord-bot-hosting.tar.gz',
                'Execute: npm install',
                'Configure: .env',
                'Execute: ./start.sh'
            ]
        });

        console.log('✅ Upload do projeto concluído!');

    } catch (error) {
        console.error('❌ Erro no upload do projeto:', error);
        res.status(500).json({ error: 'Erro no upload do projeto' });
    }
});

// Servir arquivos estáticos do cliente
app.use(express.static(path.join(__dirname, 'client/public')));

// Rota catch-all para SPA
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client/public/index.html'));
});

// Inicializar servidor
const startServer = async () => {
    try {
        await createDirectories();
        
        app.listen(PORT, () => {
            console.log(`🚀 Servidor rodando na porta ${PORT}`);
            console.log(`📱 Interface: http://localhost:${PORT}`);
        });
        
        // Inicializar serviços
        require('./services/keepAlive').start();
        require('./services/botManager').initialize();
        
    } catch (error) {
        console.error('❌ Erro ao inicializar servidor:', error);
        process.exit(1);
    }
};

startServer();

module.exports = app;
