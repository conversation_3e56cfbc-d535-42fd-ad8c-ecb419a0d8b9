{"name": "discord-bot-hosting", "version": "1.0.0", "description": "Sistema de hospedagem automática de bots Discord", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "npm install && node scripts/install.js", "build": "cd client && npm run build", "install-script": "node scripts/install.js", "pm2:start": "pm2 start ecosystem.config.json", "pm2:stop": "pm2 stop discord-bot-hosting", "pm2:restart": "pm2 restart discord-bot-hosting", "pm2:logs": "pm2 logs discord-bot-hosting"}, "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "socket.io": "^4.7.2", "archiver": "^6.0.1", "extract-zip": "^2.0.1", "fs-extra": "^11.1.1", "axios": "^1.5.0", "pm2": "^5.3.0", "node-cron": "^3.0.2", "ssh2": "^1.14.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["discord", "bot", "hosting", "nodejs", "automation"], "author": "Discord Bot Hosting System", "license": "MIT"}