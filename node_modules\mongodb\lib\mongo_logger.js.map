{"version": 3, "file": "mongo_logger.js", "sourceRoot": "", "sources": ["../src/mongo_logger.ts"], "names": [], "mappings": ";;;AAAA,+BAA6B;AAE7B,+BAA+B;AAqB/B,2CAeqB;AACrB,mCAA4D;AAE5D,gBAAgB;AACH,QAAA,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;IACzC,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,OAAO;IACd,QAAQ,EAAE,UAAU;IACpB,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,MAAM;IACf,MAAM,EAAE,QAAQ;IAChB,aAAa,EAAE,MAAM;IACrB,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,GAAG,EAAE,KAAK;CACF,CAAC,CAAC;AAEZ,gBAAgB;AACH,QAAA,2BAA2B,GAAG,IAAI,CAAC;AAIhD,gBAAgB;AAChB,MAAM,gBAAiB,SAAQ,GAAmD;IAChF,YAAY,OAA2D;QACrE,MAAM,UAAU,GAAuD,EAAE,CAAC;QAC1E,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,OAAO,EAAE;YACpC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;SACjC;QAED,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;QAC5B,KAAK,CAAC,UAAU,CAAC,CAAC;IACpB,CAAC;IAED,uBAAuB,CAAC,QAAuB;QAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAW,CAAC;IACtC,CAAC;IAED,oBAAoB,CAAC,KAAa;QAChC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAA8B,CAAC;IACtD,CAAC;CACF;AAED,gBAAgB;AACH,QAAA,kBAAkB,GAAG,IAAI,gBAAgB,CAAC;IACrD,CAAC,qBAAa,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC,qBAAa,CAAC,SAAS,EAAE,CAAC,CAAC;IAC5B,CAAC,qBAAa,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,CAAC,qBAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC3B,CAAC,qBAAa,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,CAAC,qBAAa,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1B,CAAC,qBAAa,CAAC,MAAM,EAAE,CAAC,CAAC;IACzB,CAAC,qBAAa,CAAC,aAAa,EAAE,CAAC,CAAC;IAChC,CAAC,qBAAa,CAAC,KAAK,EAAE,CAAC,CAAC;IACxB,CAAC,qBAAa,CAAC,KAAK,EAAE,CAAC,CAAC;CACzB,CAAC,CAAC;AAEH,gBAAgB;AACH,QAAA,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC;IAClD,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;IACpB,gBAAgB,EAAE,iBAAiB;IACnC,UAAU,EAAE,YAAY;CAChB,CAAC,CAAC;AAmDZ;;;;;GAKG;AACH,SAAS,uBAAuB,CAAC,CAAU;IACzC,MAAM,eAAe,GAAa,MAAM,CAAC,MAAM,CAAC,qBAAa,CAAC,CAAC;IAC/D,MAAM,aAAa,GAAG,CAAC,EAAE,WAAW,EAAE,CAAC;IAEvC,IAAI,aAAa,IAAI,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;QACpE,OAAO,aAA8B,CAAC;KACvC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,gBAAgB;AAChB,SAAgB,iBAAiB,CAAC,MAEjC;IACC,OAAO;QACL,KAAK,EAAE,CAAC,GAAQ,EAAW,EAAE;YAC3B,MAAM,CAAC,KAAK,CAAC,IAAA,cAAO,EAAC,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;YAC9E,OAAO;QACT,CAAC;KACF,CAAC;AACJ,CAAC;AATD,8CASC;AAED;;;;;;;;;GASG;AACH,SAAS,cAAc,CACrB,EAAE,gBAAgB,EAAyB,EAC3C,EAAE,cAAc,EAA+D;IAE/E,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;QAC1E,OAAO,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KAC1C;IACD,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;QAC1E,OAAO,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KAC1C;IAED,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,OAAO,cAAc,EAAE,KAAK,KAAK,UAAU,EAAE;QACrF,OAAO,cAAc,CAAC;KACvB;IAED,IAAI,gBAAgB,IAAI,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;QAC1D,OAAO,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KAC1C;IACD,IAAI,gBAAgB,IAAI,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;QAC1D,OAAO,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KAC1C;IAED,OAAO,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3C,CAAC;AAeD,SAAS,eAAe,CAAC,EAAiB,EAAE,EAAiB;IAC3D,MAAM,KAAK,GAAG,0BAAkB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;IAC7D,MAAM,KAAK,GAAG,0BAAkB,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;IAE7D,OAAO,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC;AAwBD,gBAAgB;AAChB,SAAgB,mBAAmB,CAAC,KAAU,EAAE,iBAAyB;IACvE,MAAM,KAAK,GAAG,YAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAErC,OAAO,iBAAiB,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,iBAAiB;QAChE,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,KAAK;QAC3C,CAAC,CAAC,KAAK,CAAC;AACZ,CAAC;AAND,kDAMC;AAKD,SAAS,gBAAgB,CAAC,GAAa;IACrC,MAAM,mBAAmB,GAAG,GAAqB,CAAC;IAClD,gDAAgD;IAChD,OAAO,mBAAmB,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,mBAAmB,CAAC,KAAK,KAAK,UAAU,CAAC;AACpG,CAAC;AAED,SAAS,mBAAmB,CAC1B,GAAwB,EACxB,YAA8E;IAE9E,GAAG,CAAC,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;IAC3C,GAAG,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;IACvC,GAAG,CAAC,kBAAkB,GAAG,YAAY,EAAE,YAAY,CAAC;IACpD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,mBAAW,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,CAAC;IACjF,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC;IACtB,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC;IACtB,IAAI,YAAY,EAAE,SAAS,EAAE;QAC3B,GAAG,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;KACtD;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,sBAAsB,CAC7B,GAAwB,EACxB,mBAAkD;IAElD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,mBAAW,CAAC,UAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,CAAC;IACxF,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC;IACtB,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC;IAEtB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,mBAAmB,CAC1B,SAA8C,EAC9C,oBAA4B,mCAA2B;IAEvD,IAAI,GAAG,GAA+B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAE1D,QAAQ,SAAS,CAAC,IAAI,EAAE;QACtB,KAAK,2BAAe;YAClB,GAAG,GAAG,mBAAmB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC1C,GAAG,CAAC,OAAO,GAAG,iBAAiB,CAAC;YAChC,GAAG,CAAC,OAAO,GAAG,mBAAmB,CAAC,SAAS,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YACxE,GAAG,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;YAC1C,OAAO,GAAG,CAAC;QACb,KAAK,6BAAiB;YACpB,GAAG,GAAG,mBAAmB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC1C,GAAG,CAAC,OAAO,GAAG,mBAAmB,CAAC;YAClC,GAAG,CAAC,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC;YACpC,GAAG,CAAC,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;YACpE,OAAO,GAAG,CAAC;QACb,KAAK,0BAAc;YACjB,GAAG,GAAG,mBAAmB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC1C,GAAG,CAAC,OAAO,GAAG,gBAAgB,CAAC;YAC/B,GAAG,CAAC,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC;YACpC,GAAG,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAChC,OAAO,GAAG,CAAC;QACb,KAAK,mCAAuB;YAC1B,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,yBAAyB,CAAC;YACxC,IAAI,SAAS,CAAC,OAAO,EAAE;gBACrB,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAClF,SAAS,CAAC,OAAO,CAAC;gBACpB,GAAG,GAAG;oBACJ,GAAG,GAAG;oBACN,aAAa;oBACb,WAAW;oBACX,WAAW;oBACX,aAAa;oBACb,kBAAkB;iBACnB,CAAC;aACH;YACD,OAAO,GAAG,CAAC;QACb,KAAK,iCAAqB;YACxB,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,uBAAuB,CAAC;YACtC,OAAO,GAAG,CAAC;QACb,KAAK,mCAAuB;YAC1B,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,yBAAyB,CAAC;YACxC,IAAI,SAAS,CAAC,SAAS,EAAE,SAAS,KAAK,UAAU,EAAE;gBACjD,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;aACnD;YACD,OAAO,GAAG,CAAC;QACb,KAAK,kCAAsB;YACzB,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,wBAAwB,CAAC;YACvC,OAAO,GAAG,CAAC;QACb,KAAK,8BAAkB;YACrB,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,oBAAoB,CAAC;YACnC,GAAG,CAAC,kBAAkB,GAAG,SAAS,CAAC,YAAY,CAAC;YAChD,OAAO,GAAG,CAAC;QACb,KAAK,4BAAgB;YACnB,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,kBAAkB,CAAC;YACjC,GAAG,CAAC,kBAAkB,GAAG,SAAS,CAAC,YAAY,CAAC;YAChD,OAAO,GAAG,CAAC;QACb,KAAK,6BAAiB;YACpB,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,mBAAmB,CAAC;YAClC,GAAG,CAAC,kBAAkB,GAAG,SAAS,CAAC,YAAY,CAAC;YAChD,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACxB,KAAK,OAAO;oBACV,GAAG,CAAC,MAAM,GAAG,sDAAsD,CAAC;oBACpE,MAAM;gBACR,KAAK,MAAM;oBACT,GAAG,CAAC,MAAM;wBACR,uFAAuF,CAAC;oBAC1F,MAAM;gBACR,KAAK,OAAO;oBACV,GAAG,CAAC,MAAM,GAAG,8CAA8C,CAAC;oBAC5D,IAAI,SAAS,CAAC,KAAK,EAAE;wBACnB,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;qBAC7B;oBACD,MAAM;gBACR,KAAK,YAAY;oBACf,GAAG,CAAC,MAAM,GAAG,4BAA4B,CAAC;oBAC1C,MAAM;gBACR;oBACE,GAAG,CAAC,MAAM,GAAG,yBAAyB,SAAS,CAAC,MAAM,EAAE,CAAC;aAC5D;YACD,OAAO,GAAG,CAAC;QACb,KAAK,wCAA4B;YAC/B,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,6BAA6B,CAAC;YAC5C,OAAO,GAAG,CAAC;QACb,KAAK,uCAA2B;YAC9B,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,4BAA4B,CAAC;YAC3C,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACxB,KAAK,YAAY;oBACf,GAAG,CAAC,MAAM,GAAG,4BAA4B,CAAC;oBAC1C,MAAM;gBACR,KAAK,SAAS;oBACZ,GAAG,CAAC,MAAM,GAAG,oEAAoE,CAAC;oBAClF,MAAM;gBACR,KAAK,iBAAiB;oBACpB,GAAG,CAAC,MAAM,GAAG,8DAA8D,CAAC;oBAC5E,IAAI,SAAS,CAAC,KAAK,EAAE;wBACnB,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;qBAC7B;oBACD,MAAM;gBACR;oBACE,GAAG,CAAC,MAAM,GAAG,yBAAyB,SAAS,CAAC,MAAM,EAAE,CAAC;aAC5D;YACD,OAAO,GAAG,CAAC;QACb,KAAK,kCAAsB;YACzB,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,wBAAwB,CAAC;YAEvC,GAAG,CAAC,kBAAkB,GAAG,SAAS,CAAC,YAAY,CAAC;YAChD,OAAO,GAAG,CAAC;QACb,KAAK,iCAAqB;YACxB,GAAG,GAAG,sBAAsB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC7C,GAAG,CAAC,OAAO,GAAG,uBAAuB,CAAC;YACtC,GAAG,CAAC,kBAAkB,GAAG,SAAS,CAAC,YAAY,CAAC;YAChD,OAAO,GAAG,CAAC;QACb;YACE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACpD,IAAI,KAAK,IAAI,IAAI;oBAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aACrC;KACJ;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,gBAAgB;AAChB,MAAa,WAAW;IA+BtB,YAAY,OAA2B;QA1BvC;;;WAGG;QACH,UAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACrC;;;WAGG;QACH,SAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACnC;;;WAGG;QACH,SAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACnC;;;WAGG;QACH,UAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACrC;;;WAGG;QACH,UAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAGnC,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;QACvD,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACnD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;IAC/C,CAAC;IAEO,GAAG,CACT,QAAuB,EACvB,SAAiC,EACjC,OAA0B;QAE1B,IAAI,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;YAAE,OAAO;QAE/E,IAAI,UAAU,GAAQ,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC;QACnE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;SAC9B;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACtC,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAAE;gBAC7B,UAAU,GAAG,EAAE,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;aACpD;iBAAM;gBACL,UAAU,GAAG,EAAE,GAAG,UAAU,EAAE,GAAG,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;aACzF;SACF;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,MAAM,CAAC,cAAc,CACnB,UAAiC,EACjC,aAA4C;QAE5C,kDAAkD;QAClD,MAAM,eAAe,GAAG;YACtB,GAAG,UAAU;YACb,GAAG,aAAa;YAChB,cAAc,EAAE,cAAc,CAAC,UAAU,EAAE,aAAa,CAAC;SAC1D,CAAC;QACF,MAAM,eAAe,GACnB,uBAAuB,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,qBAAa,CAAC,GAAG,CAAC;QAEhF,OAAO;YACL,mBAAmB,EAAE;gBACnB,OAAO,EAAE,uBAAuB,CAAC,eAAe,CAAC,mBAAmB,CAAC,IAAI,eAAe;gBACxF,QAAQ,EAAE,uBAAuB,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,eAAe;gBAC1F,eAAe,EACb,uBAAuB,CAAC,eAAe,CAAC,4BAA4B,CAAC,IAAI,eAAe;gBAC1F,UAAU,EACR,uBAAuB,CAAC,eAAe,CAAC,sBAAsB,CAAC,IAAI,eAAe;gBACpF,OAAO,EAAE,eAAe;aACzB;YACD,iBAAiB,EACf,IAAA,4BAAoB,EAAC,eAAe,CAAC,+BAA+B,CAAC,IAAI,IAAI;YAC/E,cAAc,EAAE,eAAe,CAAC,cAAc;SAC/C,CAAC;IACJ,CAAC;CACF;AAlGD,kCAkGC"}